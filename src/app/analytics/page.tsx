import { redirect } from 'next/navigation'
import { Header } from '@/components/header'
import { AnalyticsDashboard } from '@/components/analytics-dashboard'
import { getCurrentUser } from '@/lib/auth'

export default async function AnalyticsPage() {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect('/sign-in')
  }

  return (
    <div className="bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Analytics & Insights
          </h1>
          <p className="text-gray-600">
            Real-time analytics for benefit searches, company views, and platform trends
          </p>
        </div>

        <AnalyticsDashboard />
      </main>
    </div>
  )
}
